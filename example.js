import { chat, chatStream, generateImage, getProviders, getModels } from './app.js';

/**
 * Example usage of the AI App functions
 */

// Example 1: Simple chat (non-streaming)
async function simpleChat() {
    console.log('=== Simple Chat Example ===');
    
    try {
        const response = await chat(
            'pollinations',           // provider
            'gpt-4.1',               // model
            'Hello! How are you?'    // prompt
        );
        
        console.log('Response:', response.choices[0].message.content);
    } catch (error) {
        console.error('Chat error:', error);
    }
}

// Example 2: Streaming chat
async function streamingChat() {
    console.log('\n=== Streaming Chat Example ===');
    
    let fullResponse = '';
    
    try {
        await chatStream(
            'pollinations',                    // provider
            'gpt-4.1',                        // model
            'Write a short poem about AI',    // prompt
            (chunk) => {                      // onChunk callback
                if (chunk.choices?.[0]?.delta?.content) {
                    const content = chunk.choices[0].delta.content;
                    process.stdout.write(content); // Stream to console
                    fullResponse += content;
                }
            },
            { temperature: 0.7 }              // options
        );
        
        console.log('\n\nFull response received:', fullResponse.length, 'characters');
    } catch (error) {
        console.error('Streaming error:', error);
    }
}

// Example 3: Chat with conversation history
async function conversationChat() {
    console.log('\n=== Conversation Chat Example ===');
    
    const messages = [
        { role: 'user', content: 'What is the capital of France?' },
        { role: 'assistant', content: 'The capital of France is Paris.' },
        { role: 'user', content: 'What is its population?' }
    ];
    
    try {
        const response = await chat(
            'pollinations',
            'gpt-4.1',
            messages  // Pass messages array instead of string
        );
        
        console.log('Response:', response.choices[0].message.content);
    } catch (error) {
        console.error('Conversation error:', error);
    }
}

// Example 4: Image generation
async function imageGeneration() {
    console.log('\n=== Image Generation Example ===');
    
    try {
        const response = await generateImage(
            'pollinations',                           // provider
            'flux',                                   // model
            'A beautiful sunset over mountains',      // prompt
            { 
                size: '1024x1024',                   // options
                quality: 'standard'
            }
        );
        
        console.log('Image URL:', response.data[0].url);
    } catch (error) {
        console.error('Image generation error:', error);
    }
}

// Example 5: Get available providers and models
async function listProvidersAndModels() {
    console.log('\n=== Available Providers and Models ===');
    
    const providers = getProviders();
    console.log('Available providers:', providers);
    
    for (const provider of providers) {
        try {
            console.log(`\n${provider.toUpperCase()} models:`);
            const models = await getModels(provider);
            
            // Show first 5 models for each provider
            const chatModels = models.filter(m => m.type === 'chat').slice(0, 5);
            const imageModels = models.filter(m => m.type === 'image').slice(0, 3);
            
            if (chatModels.length > 0) {
                console.log('  Chat models:', chatModels.map(m => m.id).join(', '));
            }
            if (imageModels.length > 0) {
                console.log('  Image models:', imageModels.map(m => m.id).join(', '));
            }
        } catch (error) {
            console.log(`  Error getting models: ${error.message}`);
        }
    }
}

// Example 6: Advanced streaming with custom handling
async function advancedStreaming() {
    console.log('\n=== Advanced Streaming Example ===');
    
    let wordCount = 0;
    let startTime = Date.now();
    
    try {
        await chatStream(
            'pollinations',
            'gpt-4.1',
            'Explain quantum computing in simple terms',
            (chunk) => {
                if (chunk.choices?.[0]?.delta?.content) {
                    const content = chunk.choices[0].delta.content;
                    
                    // Count words
                    wordCount += content.split(/\s+/).filter(word => word.length > 0).length;
                    
                    // Show progress
                    if (wordCount % 10 === 0) {
                        const elapsed = (Date.now() - startTime) / 1000;
                        console.log(`\n[Progress: ${wordCount} words in ${elapsed.toFixed(1)}s]`);
                    }
                    
                    process.stdout.write(content);
                }
                
                // Handle other chunk types
                if (chunk.choices?.[0]?.finish_reason) {
                    console.log(`\n[Finished: ${chunk.choices[0].finish_reason}]`);
                }
            },
            {
                max_tokens: 200,
                temperature: 0.5
            }
        );
    } catch (error) {
        console.error('Advanced streaming error:', error);
    }
}

// Example 7: Error handling and fallback
async function errorHandlingExample() {
    console.log('\n=== Error Handling Example ===');
    
    const providers = ['pollinations', 'together', 'custom'];
    const prompt = 'What is machine learning?';
    
    for (const provider of providers) {
        try {
            console.log(`Trying ${provider}...`);
            const response = await chat(provider, 'gpt-4', prompt);
            console.log(`Success with ${provider}:`, response.choices[0].message.content.substring(0, 100) + '...');
            break; // Stop on first success
        } catch (error) {
            console.log(`${provider} failed:`, error.message);
            // Continue to next provider
        }
    }
}

// Run all examples
async function runExamples() {
    console.log('🤖 AI App Examples\n');
    
    await listProvidersAndModels();
    await simpleChat();
    await conversationChat();
    await streamingChat();
    await advancedStreaming();
    await imageGeneration();
    await errorHandlingExample();
    
    console.log('\n✅ All examples completed!');
}

// Export functions for individual testing
export {
    simpleChat,
    streamingChat,
    conversationChat,
    imageGeneration,
    listProvidersAndModels,
    advancedStreaming,
    errorHandlingExample,
    runExamples
};

// Run examples if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runExamples().catch(console.error);
}

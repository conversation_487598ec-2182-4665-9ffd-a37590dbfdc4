import { PollinationsAI, Together, Custom, DeepInfra, Puter, HuggingFace } from './ai.js';

/**
 * AI App - Simple function-based interface for AI providers with streaming support
 */
class AIApp {
    constructor() {
        this.providers = {};
        this.initializeProviders();
    }

    /**
     * Initialize all available AI providers
     */
    initializeProviders() {
        try {
            this.providers.pollinations = new PollinationsAI();
        } catch (e) {
            console.warn('PollinationsAI not available:', e.message);
        }

        try {
            this.providers.together = new Together();
        } catch (e) {
            console.warn('Together not available:', e.message);
        }

        try {
            this.providers.custom = new Custom();
        } catch (e) {
            console.warn('Custom not available:', e.message);
        }

        try {
            this.providers.deepinfra = new DeepInfra();
        } catch (e) {
            console.warn('DeepInfra not available:', e.message);
        }

        try {
            this.providers.puter = new Puter();
        } catch (e) {
            console.warn('Puter not available:', e.message);
        }

        try {
            this.providers.huggingface = new HuggingFace();
        } catch (e) {
            console.warn('HuggingFace not available:', e.message);
        }
    }

    /**
     * Get available providers
     * @returns {string[]} Array of available provider names
     */
    getAvailableProviders() {
        return Object.keys(this.providers);
    }

    /**
     * Get available models for a provider
     * @param {string} provider - Provider name
     * @returns {Promise<Array>} Array of available models
     */
    async getModels(provider) {
        if (!this.providers[provider]) {
            throw new Error(`Provider '${provider}' not available`);
        }

        try {
            const models = await this.providers[provider].models.list();
            return models;
        } catch (error) {
            console.error(`Failed to get models for ${provider}:`, error);
            return [];
        }
    }

    /**
     * Chat with AI (non-streaming)
     * @param {string} provider - Provider name (e.g., 'pollinations', 'together')
     * @param {string} model - Model name
     * @param {string|Array} prompt - Prompt string or messages array
     * @param {Object} options - Additional options
     * @returns {Promise<Object>} AI response
     */
    async chat(provider, model, prompt, options = {}) {
        if (!this.providers[provider]) {
            throw new Error(`Provider '${provider}' not available`);
        }

        const messages = this._formatMessages(prompt);
        
        const params = {
            model: model,
            messages: messages,
            stream: false,
            ...options
        };

        try {
            const response = await this.providers[provider].chat.completions.create(params);
            return response;
        } catch (error) {
            console.error(`Chat failed for ${provider}:`, error);
            throw error;
        }
    }

    /**
     * Chat with AI (streaming)
     * @param {string} provider - Provider name
     * @param {string} model - Model name
     * @param {string|Array} prompt - Prompt string or messages array
     * @param {Function} onChunk - Callback for each chunk
     * @param {Object} options - Additional options
     * @returns {Promise<void>}
     */
    async chatStream(provider, model, prompt, onChunk, options = {}) {
        if (!this.providers[provider]) {
            throw new Error(`Provider '${provider}' not available`);
        }

        if (typeof onChunk !== 'function') {
            throw new Error('onChunk callback is required for streaming');
        }

        const messages = this._formatMessages(prompt);
        
        const params = {
            model: model,
            messages: messages,
            stream: true,
            ...options
        };

        try {
            const stream = await this.providers[provider].chat.completions.create(params);
            
            for await (const chunk of stream) {
                onChunk(chunk);
            }
        } catch (error) {
            console.error(`Streaming chat failed for ${provider}:`, error);
            throw error;
        }
    }

    /**
     * Generate image
     * @param {string} provider - Provider name
     * @param {string} model - Model name
     * @param {string} prompt - Image prompt
     * @param {Object} options - Additional options
     * @returns {Promise<Object>} Image generation response
     */
    async generateImage(provider, model, prompt, options = {}) {
        if (!this.providers[provider]) {
            throw new Error(`Provider '${provider}' not available`);
        }

        if (!this.providers[provider].images) {
            throw new Error(`Provider '${provider}' does not support image generation`);
        }

        const params = {
            model: model,
            prompt: prompt,
            ...options
        };

        try {
            const response = await this.providers[provider].images.generate(params);
            return response;
        } catch (error) {
            console.error(`Image generation failed for ${provider}:`, error);
            throw error;
        }
    }

    /**
     * Format prompt into messages array
     * @param {string|Array} prompt - Prompt string or messages array
     * @returns {Array} Messages array
     */
    _formatMessages(prompt) {
        if (Array.isArray(prompt)) {
            return prompt;
        }
        
        return [{ role: 'user', content: prompt }];
    }
}

// Create global instance
const aiApp = new AIApp();

/**
 * Simple chat function
 * @param {string} provider - Provider name
 * @param {string} model - Model name
 * @param {string|Array} prompt - Prompt
 * @param {Object} options - Options
 * @returns {Promise<Object>} Response
 */
export async function chat(provider, model, prompt, options = {}) {
    return await aiApp.chat(provider, model, prompt, options);
}

/**
 * Streaming chat function
 * @param {string} provider - Provider name
 * @param {string} model - Model name
 * @param {string|Array} prompt - Prompt
 * @param {Function} onChunk - Chunk callback
 * @param {Object} options - Options
 */
export async function chatStream(provider, model, prompt, onChunk, options = {}) {
    return await aiApp.chatStream(provider, model, prompt, onChunk, options);
}

/**
 * Image generation function
 * @param {string} provider - Provider name
 * @param {string} model - Model name
 * @param {string} prompt - Image prompt
 * @param {Object} options - Options
 * @returns {Promise<Object>} Response
 */
export async function generateImage(provider, model, prompt, options = {}) {
    return await aiApp.generateImage(provider, model, prompt, options);
}

/**
 * Get available providers
 * @returns {string[]} Provider names
 */
export function getProviders() {
    return aiApp.getAvailableProviders();
}

/**
 * Get models for a provider
 * @param {string} provider - Provider name
 * @returns {Promise<Array>} Models array
 */
export async function getModels(provider) {
    return await aiApp.getModels(provider);
}

// Export the main class and instance
export { AIApp, aiApp };
export default aiApp;

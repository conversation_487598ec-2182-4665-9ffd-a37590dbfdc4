
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>GPT Chat UI</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: sans-serif;
      margin: 0;
      background: #f8f9fa;
    }
    .container {
      display: flex;
      flex-direction: column;
      flex: 1;
      max-width: 1200px;
      margin: auto;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .block {
      display: block;
    }
    #topbar {
      padding: 1rem;
      background: #e9ecef;
      border-bottom: 1px solid #ccc;
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      align-items: center;
      justify-content: space-between;
    }
    #chat {
      flex: 1;
      overflow-y: auto;
      padding: 1rem;
    }
    .message {
      margin-bottom: 1rem;
    }
    .user {
      text-align: right;
      font-weight: bold;
    }
    .assistant {
      background: #fff;
      padding: 1rem;
      border-radius: 10px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      max-width: 80%;
    }
    .assistant img {
      max-width: 100%;
      border-radius: 8px;
    }
    #form {
      display: flex;
      padding: 1rem;
      background: #fff;
      border-top: 1px solid #ccc;
    }
    #input {
      flex: 1;
      padding: 0.75rem;
      font-size: 1rem;
      border: 1px solid #ccc;
      border-radius: 5px;
    }
    #submit {
      margin-left: 0.5rem;
      padding: 0.75rem 1.2rem;
      font-size: 1rem;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
    }
    select, input[type="text"] {
      padding: 0.4rem;
      font-size: 0.9rem;
    }
    select {
      max-width: 250px;
    }
    code {
      background: #eee;
      padding: 2px 4px;
      border-radius: 4px;
      font-family: monospace;
    }
    pre {
      background: #eee;
      padding: 1em;
      overflow-x: auto;
      border-radius: 8px;
    }
  </style>
</head>
<body>
<div class="container">
<div id="topbar">
  <div class="block">
    <label>Provider:
      <select id="provider">
        <option value="pollinations">Pollinations.AI</option>
        <option value="airforce">Api.Airforce</option>
        <option value="deepinfra">DeepInfra</option>
        <option value="huggingface">HuggingFace</option>
        <option value="together">Together</option>
        <option value="puter">Puter.js</option>
        <option value="azure">Azure (http://g4f.dev/api/Azure)</option>
      </select>
    </label>
  </div>
  <div class="block">
    <label>Model:
    <select id="modelSelect"><option>Loading...</option></select>
  </label>
  </div>
  <div class="block">
      <label>API Key: <input type="text" id="apiKey" placeholder="Optional" /></label>
  </div>
</div>

<div id="chat"></div>

<form id="form">
  <input type="text" id="input" placeholder="Ask something..." autocomplete="off" />
  <button id="submit">Send</button>
</form>
</div>
<script src="https://cdn.jsdelivr.net/npm/jsencrypt/bin/jsencrypt.min.js"></script>
<script src="../dist/js/framework.js"></script>
<script type="module">
  //import { Client, PollinationsAI, DeepInfra, Together, Puter, HuggingFace } from 'https://g4f.dev/dist/js/client.js';
  import { Client, PollinationsAI, DeepInfra, Together, Puter, HuggingFace } from "../dist/js/client.js";
  
  import { marked } from "https://cdn.jsdelivr.net/npm/marked/lib/marked.esm.js";

  const chat = document.getElementById('chat');
  const form = document.getElementById('form');
  const input = document.getElementById('input');
  const providerSelect = document.getElementById('provider');
  const apiKeyInput = document.getElementById('apiKey');
  const modelSelect = document.getElementById('modelSelect');

  let client = null;
  let messages = [];

  // Set the initial height of the container to fit the viewport
  document.querySelector(".container").style.maxHeight = window.innerHeight + "px";

  async function initClient() {
    const provider = providerSelect.value;
    const apiKey = apiKeyInput.value.trim();
    const options = apiKey ? { apiKey } : {};
    let ClientClass;

    switch (provider) {
      case 'pollinations':
        ClientClass = PollinationsAI;
        break;
      case 'deepinfra':
        ClientClass = DeepInfra;
        break;
      case 'huggingface':
        ClientClass = HuggingFace;
        break;
      case 'together':
        ClientClass = Together;
        break;
      case 'puter':
        ClientClass = Puter;
        break;
      case 'azure':
        ClientClass = Client;
        break;
      case 'airforce':
        ClientClass = Client;
        options.baseUrl = 'https://api.airforce/v1';
        options.apiKey = localStorage.getItem('ApiAirforce-api_key');
        break;
    }

    client = new ClientClass(options);
    await loadModels();
  }

  async function loadModels() {
    modelSelect.innerHTML = '<option disabled selected>Loading...</option>';
    try {
      const models = await client.models.list();
      modelSelect.innerHTML = '';
      models.forEach(model => {
        const opt = document.createElement('option');
        opt.value = model.id;
        opt.textContent = model.id + (model.type == "image" ? " 🎨" : "");
        // Store the model type (e.g., 'chat' or 'image') in a data attribute.
        if (model.type) {
          opt.dataset.type = model.type;
        }
        if (model.id === client.defaultModel) opt.selected = true;
        modelSelect.appendChild(opt);
      });
    } catch (err) {
      console.error('Model load failed:', err);
      modelSelect.innerHTML = "";
    }
  }

  providerSelect.addEventListener('change', initClient);
  apiKeyInput.addEventListener('change', initClient);

  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    const text = input.value.trim();
    if (!text) return;

    // Get the full selected option element to access its data attributes.
    const selectedOption = modelSelect.options[modelSelect.selectedIndex];
    const selectedModel = selectedOption.value || client.defaultModel;
    const modelType = selectedOption.dataset.type || 'chat'; // Default to 'chat' if type is not specified

    addMessage('user', text);
    messages.push({ role: 'user', content: text });
    input.value = '';

    const assistantEl = document.createElement('div');
    assistantEl.className = 'message assistant';
    assistantEl.innerHTML = `<em>Generating with ${modelType} model...</em>`;
    chat.appendChild(assistantEl);
    chat.scrollTop = chat.scrollHeight;

    try {
      // Conditionally call the correct client method based on model type.
      if (modelType === 'image') {
        // Handle image generation
        const response = await client.images.generate({
          model: selectedModel,
          prompt: text,
        });
        
        const imageUrl = response.data[0].url;
        assistantEl.innerHTML = `<img src="${imageUrl}" alt="${text}" />`;
        messages.push({ role: 'assistant', content: `Image generated: ${imageUrl}` });

      } else {
        // Handle chat completion (existing logic)
        const stream = await client.chat.completions.create({
          model: selectedModel,
          messages,
          stream: true
        });

        let fullResponse = '';
        let isReasoning = false;
        for await (const chunk of stream) {
          let delta;
          if (chunk.choices[0]?.delta?.reasoning_content) {
            delta = chunk.choices[0]?.delta?.reasoning_content;
            isReasoning = true;
          } else {
            delta = chunk.choices[0]?.delta?.content || '';
            if (isReasoning) {
              isReasoning = false;
              delta = '\n\n---\n\n' + delta;
            }
          }
          if (!delta) continue; // Skip empty chunks
          fullResponse += delta;
          assistantEl.innerHTML = marked.parse(fullResponse);
          chat.scrollTop = chat.scrollHeight;
        }
        messages.push({ role: 'assistant', content: fullResponse });
      }
    } catch (err) {
      assistantEl.innerHTML = '<strong>Error:</strong> ' + err.message;
      console.error(err);
    }
  });

  function addMessage(role, content) {
    const el = document.createElement('div');
    el.className = 'message ' + role;
    el.innerHTML = role === 'user'
      ? `<div class="user">${content}</div>`
      : marked.parse(content);
    chat.appendChild(el);
    chat.scrollTop = chat.scrollHeight;
  }

  await initClient();
</script>

</body>
</html>

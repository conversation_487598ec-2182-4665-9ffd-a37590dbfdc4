<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI App Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        select, input, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            min-height: 100px;
        }
        .streaming {
            border-left: 4px solid #007bff;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
        .models-list {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
        }
        .model-item {
            padding: 5px;
            margin: 2px 0;
            background: white;
            border-radius: 3px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🤖 AI App Demo</h1>
    
    <div class="container">
        <h2>Available Providers & Models</h2>
        <button onclick="loadProviders()">Load Providers & Models</button>
        <div id="providers-list"></div>
    </div>

    <div class="container">
        <h2>Chat (Non-Streaming)</h2>
        <div class="form-group">
            <label for="chat-provider">Provider:</label>
            <select id="chat-provider">
                <option value="pollinations">Pollinations AI</option>
                <option value="together">Together</option>
                <option value="puter">Puter</option>
                <option value="custom">Custom</option>
            </select>
        </div>
        <div class="form-group">
            <label for="chat-model">Model:</label>
            <input type="text" id="chat-model" value="gpt-4.1" placeholder="Enter model name">
        </div>
        <div class="form-group">
            <label for="chat-prompt">Prompt:</label>
            <textarea id="chat-prompt" rows="3" placeholder="Enter your prompt here...">Hello! How are you today?</textarea>
        </div>
        <button onclick="sendChat()">Send Chat</button>
        <div id="chat-response" class="response"></div>
    </div>

    <div class="container">
        <h2>Chat (Streaming)</h2>
        <div class="form-group">
            <label for="stream-provider">Provider:</label>
            <select id="stream-provider">
                <option value="pollinations">Pollinations AI</option>
                <option value="together">Together</option>
                <option value="puter">Puter</option>
                <option value="custom">Custom</option>
            </select>
        </div>
        <div class="form-group">
            <label for="stream-model">Model:</label>
            <input type="text" id="stream-model" value="gpt-4.1" placeholder="Enter model name">
        </div>
        <div class="form-group">
            <label for="stream-prompt">Prompt:</label>
            <textarea id="stream-prompt" rows="3" placeholder="Enter your prompt here...">Write a short story about a robot learning to paint.</textarea>
        </div>
        <button onclick="sendStreamChat()">Send Streaming Chat</button>
        <div id="stream-response" class="response streaming"></div>
    </div>

    <div class="container">
        <h2>Image Generation</h2>
        <div class="form-group">
            <label for="image-provider">Provider:</label>
            <select id="image-provider">
                <option value="pollinations">Pollinations AI</option>
                <option value="together">Together</option>
            </select>
        </div>
        <div class="form-group">
            <label for="image-model">Model:</label>
            <input type="text" id="image-model" value="flux" placeholder="Enter image model name">
        </div>
        <div class="form-group">
            <label for="image-prompt">Prompt:</label>
            <textarea id="image-prompt" rows="2" placeholder="Describe the image you want...">A beautiful sunset over mountains with a lake reflection</textarea>
        </div>
        <button onclick="generateImage()">Generate Image</button>
        <div id="image-response" class="response"></div>
    </div>

    <script type="module">
        import { chat, chatStream, generateImage, getProviders, getModels } from './app.js';
        
        // Make functions available globally
        window.chat = chat;
        window.chatStream = chatStream;
        window.generateImage = generateImage;
        window.getProviders = getProviders;
        window.getModels = getModels;
        
        // Load providers on page load
        window.addEventListener('load', () => {
            loadProviders();
        });
        
        window.loadProviders = async function() {
            const providersDiv = document.getElementById('providers-list');
            providersDiv.innerHTML = '<div class="loading">Loading providers and models...</div>';
            
            try {
                const providers = getProviders();
                let html = '<h3>Available Providers:</h3>';
                
                for (const provider of providers) {
                    html += `<h4>${provider.toUpperCase()}</h4>`;
                    try {
                        const models = await getModels(provider);
                        const chatModels = models.filter(m => m.type === 'chat').slice(0, 10);
                        const imageModels = models.filter(m => m.type === 'image').slice(0, 5);
                        
                        html += '<div class="models-list">';
                        if (chatModels.length > 0) {
                            html += '<strong>Chat Models:</strong><br>';
                            chatModels.forEach(model => {
                                html += `<div class="model-item">📝 ${model.id}</div>`;
                            });
                        }
                        if (imageModels.length > 0) {
                            html += '<strong>Image Models:</strong><br>';
                            imageModels.forEach(model => {
                                html += `<div class="model-item">🎨 ${model.id}</div>`;
                            });
                        }
                        html += '</div>';
                    } catch (error) {
                        html += `<div class="error">Error loading models: ${error.message}</div>`;
                    }
                }
                
                providersDiv.innerHTML = html;
            } catch (error) {
                providersDiv.innerHTML = `<div class="error">Error loading providers: ${error.message}</div>`;
            }
        };
        
        window.sendChat = async function() {
            const provider = document.getElementById('chat-provider').value;
            const model = document.getElementById('chat-model').value;
            const prompt = document.getElementById('chat-prompt').value;
            const responseDiv = document.getElementById('chat-response');
            
            responseDiv.innerHTML = '<div class="loading">Sending request...</div>';
            responseDiv.className = 'response';
            
            try {
                const response = await chat(provider, model, prompt);
                responseDiv.innerHTML = response.choices[0].message.content;
            } catch (error) {
                responseDiv.innerHTML = `Error: ${error.message}`;
                responseDiv.className = 'response error';
            }
        };
        
        window.sendStreamChat = async function() {
            const provider = document.getElementById('stream-provider').value;
            const model = document.getElementById('stream-model').value;
            const prompt = document.getElementById('stream-prompt').value;
            const responseDiv = document.getElementById('stream-response');
            
            responseDiv.innerHTML = '';
            responseDiv.className = 'response streaming';
            
            try {
                await chatStream(provider, model, prompt, (chunk) => {
                    if (chunk.choices?.[0]?.delta?.content) {
                        responseDiv.innerHTML += chunk.choices[0].delta.content;
                    }
                });
            } catch (error) {
                responseDiv.innerHTML = `Error: ${error.message}`;
                responseDiv.className = 'response error';
            }
        };
        
        window.generateImage = async function() {
            const provider = document.getElementById('image-provider').value;
            const model = document.getElementById('image-model').value;
            const prompt = document.getElementById('image-prompt').value;
            const responseDiv = document.getElementById('image-response');
            
            responseDiv.innerHTML = '<div class="loading">Generating image...</div>';
            responseDiv.className = 'response';
            
            try {
                const response = await generateImage(provider, model, prompt);
                const imageUrl = response.data[0].url;
                responseDiv.innerHTML = `<img src="${imageUrl}" alt="Generated image" style="max-width: 100%; border-radius: 4px;">`;
            } catch (error) {
                responseDiv.innerHTML = `Error: ${error.message}`;
                responseDiv.className = 'response error';
            }
        };
    </script>
</body>
</html>
